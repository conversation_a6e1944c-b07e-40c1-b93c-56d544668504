<template>
  <div class="snow-page">
    <div class="home-page">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">首页</h1>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧内容 -->
        <div class="left-content">
          <!-- 主标题区域 -->
          <div class="hero-section">
            <h2 class="main-title">数据赋能教与学 开启教育新时代</h2>
            <p class="subtitle">
              数据与AI技术深度融合，创新教育理论，推动教育全面发展，<br />
              为教师提供强大教学工具，为学习者打造个性化、高效学习环境
            </p>
          </div>

          <!-- 功能卡片网格 -->
          <div class="feature-grid">
            <!-- 第一行 -->
            <div class="feature-row">
              <div class="feature-card highlighted">
                <div class="card-icon">
                  <s-svg-icon name="智能阅卷" size="60" />
                </div>
                <h3 class="card-title">智能阅卷</h3>
                <p class="card-desc">高精度识别所有的网答题卡，无需二次核对，降低复合成本</p>
                <div class="card-arrow">
                  <icon-right></icon-right>
                </div>
              </div>

              <div class="feature-card">
                <div class="card-icon">
                  <s-svg-icon name="考试阅卷" size="60" />
                </div>
                <h3 class="card-title">考试阅卷</h3>
                <p class="card-desc">高对支持传统、手机阅卷拍照等，多种形式的方式，真正高效</p>
                <div class="card-arrow">
                  <icon-right />
                </div>
              </div>

              <div class="feature-card">
                <div class="card-icon">
                  <s-svg-icon name="学情分析" size="60" />
                </div>
                <h3 class="card-title">学情分析</h3>
                <p class="card-desc">多维度、多维度、多角度、全方式的大数据分析，科学全面、反馈深入</p>
                <div class="card-arrow">
                  <icon-right />
                </div>
              </div>

              <div class="feature-card">
                <div class="card-icon">
                  <s-svg-icon name="作业批改" size="60" />
                </div>
                <h3 class="card-title">作业批改无人机</h3>
                <p class="card-desc">自动代替老师批改文字、生成作文常见综合分析报告</p>
                <div class="card-arrow">
                  <icon-right />
                </div>
              </div>
            </div>

            <!-- 第二行 -->
            <div class="feature-row">
              <div class="feature-card">
                <div class="card-icon">
                  <s-svg-icon name="错题本" size="60" />
                </div>
                <h3 class="card-title">五段错题本</h3>
                <p class="card-desc">学生可按日、周、月、年等各种维度，进行错题复习</p>
                <div class="card-arrow">
                  <icon-right />
                </div>
              </div>

              <div class="feature-card">
                <div class="card-icon">
                  <s-svg-icon name="选题组卷" size="60" />
                </div>
                <h3 class="card-title">选题组卷</h3>
                <p class="card-desc">一键高效、智能的组卷，为教师们提供了强大的教学方案</p>
                <div class="card-arrow">
                  <icon-right />
                </div>
              </div>

              <div class="feature-card">
                <div class="card-icon">
                  <s-svg-icon name="基础信息" size="60" />
                </div>
                <h3 class="card-title">基础信息设置</h3>
                <p class="card-desc">班级管理、试卷、科目、学生、教师等基本信息录入</p>
                <div class="card-arrow">
                  <icon-right />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧内容 -->
        <div class="right-content">
          <div class="evaluation-section">
            <h3 class="section-title">作业测评</h3>
            <p class="section-desc">高精度识别所有的网答题卡，无需二次核对，降低复合成本</p>

            <div class="evaluation-options">
              <div class="eval-option">
                <div class="eval-icon">
                  <s-svg-icon name="手机" size="24" />
                </div>
                <span>手机拍照测评</span>
                <icon-right class="eval-arrow" />
              </div>

              <div class="eval-option">
                <div class="eval-icon">
                  <s-svg-icon name="阅卷机" size="24" />
                </div>
                <span>阅卷机测评</span>
                <icon-right class="eval-arrow" />
              </div>

              <div class="eval-option">
                <div class="eval-icon">
                  <s-svg-icon name="智能题" size="24" />
                </div>
                <span>智能题测评</span>
                <icon-right class="eval-arrow" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts"></script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  padding: 0;
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
  .page-header {
    padding: 20px 40px;
    background: rgb(255 255 255 / 80%);
    border-bottom: 1px solid #e5e7eb;
    backdrop-filter: blur(10px);
    .page-title {
      position: relative;
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #1f2937;
      &::after {
        position: absolute;
        bottom: -8px;
        left: 0;
        width: 40px;
        height: 3px;
        content: "";
        background: linear-gradient(90deg, #4080ff 0%, #1890ff 100%);
        border-radius: 2px;
      }
    }
  }
  .main-content {
    display: flex;
    gap: 40px;
    max-width: 1400px;
    padding: 40px;
    margin: 0 auto;
  }
  .left-content {
    flex: 1;
    .hero-section {
      margin-bottom: 50px;
      .main-title {
        margin: 0 0 20px;
        font-size: 36px;
        font-weight: 700;
        line-height: 1.3;
        color: #1f2937;
        letter-spacing: 1px;
      }
      .subtitle {
        margin: 0;
        font-size: 16px;
        line-height: 1.6;
        color: #6b7280;
      }
    }
    .feature-grid {
      .feature-row {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 24px;
        margin-bottom: 24px;
        &:last-child {
          grid-template-columns: repeat(3, 1fr);
        }
      }
    }
  }
  .feature-card {
    position: relative;
    padding: 24px;
    text-align: center;
    cursor: pointer;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    transition: all 0.3s ease;
    &:hover {
      border-color: #4080ff;
      box-shadow: 0 8px 25px rgb(64 128 255 / 15%);
      transform: translateY(-4px);
    }
    &.highlighted {
      background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
      border-color: #4080ff;
    }
    .card-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 80px;
      margin-bottom: 16px;
    }
    .card-title {
      margin: 0 0 12px;
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
    }
    .card-desc {
      margin: 0 0 20px;
      font-size: 14px;
      line-height: 1.5;
      color: #6b7280;
    }
    .card-arrow {
      position: absolute;
      right: 16px;
      bottom: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      color: #6b7280;
      background: #f3f4f6;
      border-radius: 50%;
      transition: all 0.3s ease;
    }
    &:hover .card-arrow {
      color: white;
      background: #4080ff;
    }
  }
  .right-content {
    width: 300px;
    .evaluation-section {
      padding: 24px;
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 12px rgb(0 0 0 / 5%);
      .section-title {
        margin: 0 0 12px;
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
      }
      .section-desc {
        margin: 0 0 24px;
        font-size: 14px;
        line-height: 1.5;
        color: #6b7280;
      }
      .evaluation-options {
        .eval-option {
          display: flex;
          align-items: center;
          padding: 16px;
          margin-bottom: 12px;
          cursor: pointer;
          border-radius: 12px;
          transition: all 0.3s ease;
          &:hover {
            background: #f8faff;
          }
          &:last-child {
            margin-bottom: 0;
          }
          .eval-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            margin-right: 12px;
            color: white;
            background: linear-gradient(135deg, #4080ff 0%, #1890ff 100%);
            border-radius: 10px;
          }
          span {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
          }
          .eval-arrow {
            font-size: 16px;
            color: #9ca3af;
          }
        }
      }
    }
  }
}
</style>
